[doctest] run with "--help" for options
===============================================================================
foo.h(0):
TEST CASE:  [return42] bartest

foo.h(0): MESSAGE: 
  logged: Running [return42] bartest

===============================================================================
foo.h(0):
TEST CASE:  [default] bartest

foo.h(0): MESSAGE: 
  logged: Running [default] bartest

===============================================================================
foo.h(0):
TEST CASE:  [default] commontest

foo.h(0): MESSAGE: 
  logged: Running [default] commontest

===============================================================================
foo.h(0):
TEST CASE:  [return42] commontest

foo.h(0): MESSAGE: 
  logged: Running [return42] commontest

===============================================================================
main.cpp(0):
TEST CASE:  main

main.cpp(0): MESSAGE: hello from <main.cpp>

===============================================================================
test_runner.cpp(0):
TEST CASE:  test_runner

test_runner.cpp(0): MESSAGE: hello from <test_runner.cpp>

===============================================================================
[doctest] test cases: 6 | 6 passed | 0 failed | 0 skipped
[doctest] assertions: 2 | 2 passed | 0 failed |
[doctest] Status: SUCCESS!
