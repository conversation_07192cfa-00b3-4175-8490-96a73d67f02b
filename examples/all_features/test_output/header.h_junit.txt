<?xml version="1.0" encoding="UTF-8"?>
<testsuites>
  <testsuite name="all_features" errors="0" failures="3" tests="4">
    <testcase classname="header.h" name="in TS" status="run">
      <failure type="FAIL">
header.h(0):


      </failure>
    </testcase>
    <testcase classname="header.h" name="template 1&lt;char>" status="run">
      <failure type="FAIL">
header.h(0):


      </failure>
    </testcase>
    <testcase classname="header.h" name="template 2&lt;doctest::String>" status="run">
      <failure type="FAIL">
header.h(0):


      </failure>
    </testcase>
    <testcase classname="header.h" name="fixtured test" status="run"/>
  </testsuite>
</testsuites>
Program code.
