[doctest]
[doctest] boolean values: "1/on/yes/true" or "0/off/no/false"
[doctest] filter  values: "str1,str2,str3" (comma separated strings)
[doctest]
[doctest] filters use wildcards for matching strings
[doctest] something passes a filter if any of the strings in a filter matches
[doctest]
[doctest] ALL FLAGS, OPTIONS AND FILTERS ALSO AVAILABLE WITH A "dt-" PREFIX!!!
[doctest]
[doctest] Query flags - the program quits after them. Available:

 -?,   --help, -h                      prints this message
 -v,   --version                       prints the version
 -c,   --count                         prints the number of matching tests
 -ltc, --list-test-cases               lists all matching tests by name
 -lts, --list-test-suites              lists all matching test suites
 -lr,  --list-reporters                lists all registered reporters

[doctest] The available <int>/<string> options/filters are:

 -tc,  --test-case=<filters>           filters     tests by their name
 -tce, --test-case-exclude=<filters>   filters OUT tests by their name
 -sf,  --source-file=<filters>         filters     tests by their file
 -sfe, --source-file-exclude=<filters> filters OUT tests by their file
 -ts,  --test-suite=<filters>          filters     tests by their test suite
 -tse, --test-suite-exclude=<filters>  filters OUT tests by their test suite
 -sc,  --subcase=<filters>             filters     subcases by their name
 -sce, --subcase-exclude=<filters>     filters OUT subcases by their name
 -r,   --reporters=<filters>           reporters to use (console is default)
 -o,   --out=<string>                  output filename
 -ob,  --order-by=<string>             how the tests should be ordered
                                       <string> - [file/suite/name/rand/none]
 -rs,  --rand-seed=<int>               seed for random ordering
 -f,   --first=<int>                   the first test passing the filters to
                                       execute - for range-based execution
 -l,   --last=<int>                    the last test passing the filters to
                                       execute - for range-based execution
 -aa,  --abort-after=<int>             stop after <int> failed assertions
 -scfl,--subcase-filter-levels=<int>   apply filters for the first <int> levels

[doctest] Bool options - can be used like flags and true is assumed. Available:

 -s,   --success=<bool>                include successful assertions in output
 -cs,  --case-sensitive=<bool>         filters being treated as case sensitive
 -e,   --exit=<bool>                   exits after the tests finish
 -d,   --duration=<bool>               prints the time duration of each test
 -m,   --minimal=<bool>                minimal console output (only failures)
 -q,   --quiet=<bool>                  no console output
 -nt,  --no-throw=<bool>               skips exceptions-related assert checks
 -ne,  --no-exitcode=<bool>            returns (or exits) always with success
 -nr,  --no-run=<bool>                 skips all runtime doctest operations
 -ni,  --no-intro=<bool>               omit the framework intro in the output
 -nv,  --no-version=<bool>             omit the framework version in the output
 -nc,  --no-colors=<bool>              disables colors in output
 -fc,  --force-colors=<bool>           use colors even when not in a tty
 -nb,  --no-breaks=<bool>              disables breakpoints in debuggers
 -ns,  --no-skip=<bool>                don't skip test cases marked as skip
 -gfl, --gnu-file-line=<bool>          :n: vs (n): for line numbers in output
 -npf, --no-path-filenames=<bool>      only filenames and no paths in output
 -nln, --no-line-numbers=<bool>        0 instead of real line numbers in output

[doctest] for more information visit the project documentation

