[doctest] run with "--help" for options
===============================================================================
coverage_maxout.cpp(0):
TEST CASE:  exercising tricky code paths of doctest

coverage_maxout.cpp(0): ERROR: CHECK( str.compare(const_str, true) != 0 ) is NOT correct!
  values: CHECK( 0 != 0 )
  logged: should fail

coverage_maxout.cpp(0): ERROR: CHECK( str.compare("omgomgomg", false) != 0 ) is NOT correct!
  values: CHECK( 0 != 0 )
  logged: should fail

Aborting - too many failed asserts!
===============================================================================
[doctest] test cases: 1 | 0 passed | 1 failed |
[doctest] assertions: 7 | 5 passed | 2 failed |
[doctest] Status: FAILURE!
Program code.
