[doctest] run with "--help" for options
===============================================================================
header.h(0):
TEST SUITE: some TS
TEST CASE:  in TS

header.h(0): FATAL ERROR: 

===============================================================================
header.h(0):
TEST CASE:  template 1<char>

header.h(0): FATAL ERROR: 

===============================================================================
header.h(0):
TEST CASE:  template 2<doctest::String>

header.h(0): FATAL ERROR: 

===============================================================================
[doctest] test cases: 4 | 1 passed | 3 failed |
[doctest] assertions: 4 | 1 passed | 3 failed |
[doctest] Status: FAILURE!
Program code.
