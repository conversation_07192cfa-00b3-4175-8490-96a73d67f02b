<?xml version="1.0" encoding="UTF-8"?>
<doctest binary="all_features">
  <Options order_by="file" rand_seed="324" first="0" last="4294967295" abort_after="2" subcase_filter_levels="2147483647" case_sensitive="false" no_throw="false" no_skip="false"/>
  <TestSuite>
    <TestCase name="exercising tricky code paths of doctest" filename="coverage_maxout.cpp" line="0">
      <Expression success="false" type="CHECK" filename="coverage_maxout.cpp" line="0">
        <Original>
          str.compare(const_str, true) != 0
        </Original>
        <Expanded>
          0 != 0
        </Expanded>
        <Info>
          should fail
        </Info>
      </Expression>
      <Expression success="false" type="CHECK" filename="coverage_maxout.cpp" line="0">
        <Original>
          str.compare("omgomgomg", false) != 0
        </Original>
        <Expanded>
          0 != 0
        </Expanded>
        <Info>
          should fail
        </Info>
      </Expression>
      <OverallResultsAsserts successes="5" failures="2" test_case_success="false"/>
    </TestCase>
  </TestSuite>
  <OverallResultsAsserts successes="5" failures="2"/>
  <OverallResultsTestCases successes="0" failures="1"/>
</doctest>
Program code.
