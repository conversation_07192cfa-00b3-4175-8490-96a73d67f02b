<?xml version="1.0" encoding="UTF-8"?>
<doctest binary="all_features">
  <Options order_by="file" rand_seed="324" first="0" last="4294967295" abort_after="0" subcase_filter_levels="2147483647" case_sensitive="false" no_throw="false" no_skip="false"/>
  <TestSuite>
    <TestCase name="logging the counter of a loop" filename="logging.cpp" line="0">
      <Expression success="false" type="CHECK" filename="logging.cpp" line="0">
        <Original>
          vec[i] != (1 &lt;&lt; i)
        </Original>
        <Expanded>
          1 != 1
        </Expanded>
        <Info>
          current iteration of loop:
        </Info>
        <Info>
          i := 0
        </Info>
      </Expression>
      <Expression success="false" type="CHECK" filename="logging.cpp" line="0">
        <Original>
          vec[i] != (1 &lt;&lt; i)
        </Original>
        <Expanded>
          2 != 2
        </Expanded>
        <Info>
          current iteration of loop:
        </Info>
        <Info>
          i := 1
        </Info>
      </Expression>
      <Expression success="false" type="CHECK" filename="logging.cpp" line="0">
        <Original>
          vec[i] != (1 &lt;&lt; i)
        </Original>
        <Expanded>
          4 != 4
        </Expanded>
        <Info>
          current iteration of loop:
        </Info>
        <Info>
          i := 2
        </Info>
      </Expression>
      <Expression success="false" type="CHECK" filename="logging.cpp" line="0">
        <Original>
          vec[i] != (1 &lt;&lt; i)
        </Original>
        <Expanded>
          8 != 8
        </Expanded>
        <Info>
          current iteration of loop:
        </Info>
        <Info>
          i := 3
        </Info>
      </Expression>
      <Expression success="false" type="CHECK" filename="logging.cpp" line="0">
        <Original>
          vec[i] != (1 &lt;&lt; i)
        </Original>
        <Expanded>
          16 != 16
        </Expanded>
        <Info>
          current iteration of loop:
        </Info>
        <Info>
          i := 4
        </Info>
      </Expression>
      <OverallResultsAsserts successes="0" failures="5" test_case_success="false"/>
    </TestCase>
    <TestCase name="a test case that will end from an exception" filename="logging.cpp" line="0">
      <Message type="ERROR" filename="logging.cpp" line="0">
        <Text>
          forcing the many captures to be stringified
        </Text>
        <Info>
          lots of captures: 42 42 42;
        </Info>
        <Info>
          old way of capturing - using the streaming operator: 42 42
        </Info>
      </Message>
      <Expression success="false" type="CHECK" filename="logging.cpp" line="0">
        <Original>
          some_var == 666
        </Original>
        <Expanded>
          42 == 666
        </Expanded>
        <Info>
          someTests() returned: 42
        </Info>
        <Info>
          this should be printed if an exception is thrown even if no assert has failed: 42
        </Info>
        <Info>
          in a nested scope this should be printed as well: 42
        </Info>
        <Info>
          why is this not 666 ?!
        </Info>
      </Expression>
      <Exception crash="false">
        0
      </Exception>
      <OverallResultsAsserts successes="0" failures="2" test_case_success="false"/>
    </TestCase>
    <TestCase name="a test case that will end from an exception and should print the unprinted context" filename="logging.cpp" line="0">
      <Exception crash="false">
        0
      </Exception>
      <OverallResultsAsserts successes="0" failures="0" test_case_success="false"/>
    </TestCase>
    <TestCase name="third party asserts can report failures to doctest" filename="logging.cpp" line="0">
      <Message type="ERROR" filename="logging.cpp" line="0">
        <Text>
          MY_ASSERT(false)
        </Text>
      </Message>
      <Message type="FATAL ERROR" filename="logging.cpp" line="0">
        <Text>
          MY_ASSERT_FATAL(false)
        </Text>
      </Message>
      <OverallResultsAsserts successes="0" failures="2" test_case_success="false"/>
    </TestCase>
    <TestCase name="explicit failures 1" filename="logging.cpp" line="0">
      <Message type="ERROR" filename="logging.cpp" line="0">
        <Text>
          this should not end the test case, but mark it as failing
        </Text>
      </Message>
      <Message type="WARNING" filename="logging.cpp" line="0">
        <Text>
          reached!
        </Text>
      </Message>
      <OverallResultsAsserts successes="0" failures="1" test_case_success="false"/>
    </TestCase>
    <TestCase name="explicit failures 2" filename="logging.cpp" line="0">
      <Message type="FATAL ERROR" filename="logging.cpp" line="0">
        <Text>
          fail the test case and also end it
        </Text>
      </Message>
      <OverallResultsAsserts successes="0" failures="1" test_case_success="false"/>
    </TestCase>
  </TestSuite>
  <OverallResultsAsserts successes="0" failures="11"/>
  <OverallResultsTestCases successes="0" failures="6"/>
</doctest>
Program code.
