<?xml version="1.0" encoding="UTF-8"?>
<doctest binary="all_features">
  <Options order_by="file" rand_seed="324" first="0" last="4294967295" abort_after="0" subcase_filter_levels="2147483647" case_sensitive="false" no_throw="false" no_skip="false"/>
  <TestSuite>
    <TestCase name="no checks" filename="no_failures.cpp" line="0">
      <OverallResultsAsserts successes="0" failures="0" test_case_success="true"/>
    </TestCase>
    <TestCase name="simple check" filename="no_failures.cpp" line="0">
      <OverallResultsAsserts successes="1" failures="0" test_case_success="true"/>
    </TestCase>
  </TestSuite>
  <TestSuite name="some suite">
    <TestCase name="fails - and its allowed" filename="no_failures.cpp" line="0" may_fail="true">
      <Message type="FATAL ERROR" filename="no_failures.cpp" line="0">
        <Text/>
      </Message>
      <OverallResultsAsserts successes="0" failures="1" test_case_success="true"/>
    </TestCase>
  </TestSuite>
  <TestSuite>
    <TestCase name="should fail and no output" filename="no_failures.cpp" line="0" should_fail="true">
      <Message type="FATAL ERROR" filename="no_failures.cpp" line="0">
        <Text/>
      </Message>
      <OverallResultsAsserts successes="0" failures="1" test_case_success="true"/>
    </TestCase>
  </TestSuite>
  <OverallResultsAsserts successes="1" failures="2"/>
  <OverallResultsTestCases successes="4" failures="0"/>
</doctest>
Program code.
