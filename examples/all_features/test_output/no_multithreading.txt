[doctest] run with "--help" for options
===============================================================================
subcases.cpp(0):
  Scenario: vectors can be sized and resized
     Given: A vector with some items
      When: the size is increased
      Then: the size and capacity change

subcases.cpp(0): ERROR: CHECK( v.size() == 20 ) is NOT correct!
  values: CHECK( 10 == 20 )

===============================================================================
subcases.cpp(0):
  Scenario: vectors can be sized and resized
     Given: A vector with some items
      When: less capacity is reserved
      Then: neither size nor capacity are changed

subcases.cpp(0): ERROR: CHECK( v.size() == 10 ) is NOT correct!
  values: CHECK( 5 == 10 )

===============================================================================
assertion_macros.cpp(0):
TEST CASE:  CHECK level of asserts fail the test case but don't abort it

assertion_macros.cpp(0): ERROR: CHECK( 0 ) is NOT correct!
  values: CHECK( 0 )

assertion_macros.cpp(0): ERROR: CHECK_FALSE( 1 ) is NOT correct!
  values: CHECK_FALSE( 1 )

assertion_macros.cpp(0): ERROR: CHECK_THROWS( throw_if(false, 0) ) did NOT throw at all!

assertion_macros.cpp(0): ERROR: CHECK_THROWS_AS( throw_if(false, 0), bool ) did NOT throw at all!

assertion_macros.cpp(0): ERROR: CHECK_THROWS_AS( throw_if(true, 0), bool ) threw a DIFFERENT exception: "0"

assertion_macros.cpp(0): ERROR: CHECK_THROWS_WITH( throw_if(true, 0), "unrecognized" ) threw a DIFFERENT exception: "0"

assertion_macros.cpp(0): ERROR: CHECK_THROWS_WITH_AS( throw_if(true, 0), "unrecognized", int ) threw a DIFFERENT exception! (contents: "0")

assertion_macros.cpp(0): ERROR: CHECK_NOTHROW( throw_if(true, 0) ) THREW exception: "0"

assertion_macros.cpp(0): ERROR: CHECK_EQ( 1, 0 ) is NOT correct!
  values: CHECK_EQ( 1, 0 )

assertion_macros.cpp(0): ERROR: CHECK_UNARY( 0 ) is NOT correct!
  values: CHECK_UNARY( 0 )

assertion_macros.cpp(0): ERROR: CHECK_UNARY_FALSE( 1 ) is NOT correct!
  values: CHECK_UNARY_FALSE( 1 )

assertion_macros.cpp(0): MESSAGE: reached!

===============================================================================
decomposition.cpp(0):
TEST CASE:  Move Only Type

decomposition.cpp(0): ERROR: CHECK( genType(false) ) is NOT correct!
  values: CHECK( {?} )

decomposition.cpp(0): ERROR: CHECK( a ) is NOT correct!
  values: CHECK( {?} )

===============================================================================
assertion_macros.cpp(0):
TEST CASE:  REQUIRE level of asserts fail and abort the test case - 1

assertion_macros.cpp(0): FATAL ERROR: REQUIRE( 0 ) is NOT correct!
  values: REQUIRE( 0 )

===============================================================================
assertion_macros.cpp(0):
TEST CASE:  REQUIRE level of asserts fail and abort the test case - 10

assertion_macros.cpp(0): FATAL ERROR: REQUIRE_NOTHROW( throw_if(true, 0) ) THREW exception: "0"

===============================================================================
assertion_macros.cpp(0):
TEST CASE:  REQUIRE level of asserts fail and abort the test case - 11

assertion_macros.cpp(0): FATAL ERROR: REQUIRE_EQ( 1, 0 ) is NOT correct!
  values: REQUIRE_EQ( 1, 0 )

===============================================================================
assertion_macros.cpp(0):
TEST CASE:  REQUIRE level of asserts fail and abort the test case - 12

assertion_macros.cpp(0): FATAL ERROR: REQUIRE_UNARY( 0 ) is NOT correct!
  values: REQUIRE_UNARY( 0 )

===============================================================================
assertion_macros.cpp(0):
TEST CASE:  REQUIRE level of asserts fail and abort the test case - 13

assertion_macros.cpp(0): FATAL ERROR: REQUIRE_UNARY_FALSE( 1 ) is NOT correct!
  values: REQUIRE_UNARY_FALSE( 1 )

===============================================================================
assertion_macros.cpp(0):
TEST CASE:  REQUIRE level of asserts fail and abort the test case - 2

assertion_macros.cpp(0): FATAL ERROR: REQUIRE_FALSE( 1 ) is NOT correct!
  values: REQUIRE_FALSE( 1 )

===============================================================================
assertion_macros.cpp(0):
TEST CASE:  REQUIRE level of asserts fail and abort the test case - 3

assertion_macros.cpp(0): FATAL ERROR: REQUIRE_THROWS( throw_if(false, 0) ) did NOT throw at all!

===============================================================================
assertion_macros.cpp(0):
TEST CASE:  REQUIRE level of asserts fail and abort the test case - 4

assertion_macros.cpp(0): FATAL ERROR: REQUIRE_THROWS_AS( throw_if(false, 0), bool ) did NOT throw at all!

===============================================================================
assertion_macros.cpp(0):
TEST CASE:  REQUIRE level of asserts fail and abort the test case - 5

assertion_macros.cpp(0): FATAL ERROR: REQUIRE_THROWS_AS( throw_if(true, 0), bool ) threw a DIFFERENT exception: "0"

===============================================================================
assertion_macros.cpp(0):
TEST CASE:  REQUIRE level of asserts fail and abort the test case - 6

assertion_macros.cpp(0): FATAL ERROR: REQUIRE_THROWS_WITH( throw_if(false, ""), "whops!" ) did NOT throw at all!

===============================================================================
assertion_macros.cpp(0):
TEST CASE:  REQUIRE level of asserts fail and abort the test case - 7

assertion_macros.cpp(0): FATAL ERROR: REQUIRE_THROWS_WITH( throw_if(true, ""), "whops!" ) threw a DIFFERENT exception: 

===============================================================================
assertion_macros.cpp(0):
TEST CASE:  REQUIRE level of asserts fail and abort the test case - 8

assertion_macros.cpp(0): FATAL ERROR: REQUIRE_THROWS_WITH_AS( throw_if(false, ""), "whops!", bool ) did NOT throw at all!

===============================================================================
assertion_macros.cpp(0):
TEST CASE:  REQUIRE level of asserts fail and abort the test case - 9

assertion_macros.cpp(0): FATAL ERROR: REQUIRE_THROWS_WITH_AS( throw_if(true, ""), "whops!", bool ) threw a DIFFERENT exception! (contents: )

===============================================================================
assertion_macros.cpp(0):
TEST CASE:  WARN level of asserts don't fail the test case

assertion_macros.cpp(0): WARNING: WARN( 0 ) is NOT correct!
  values: WARN( 0 )

assertion_macros.cpp(0): WARNING: WARN_FALSE( 1 ) is NOT correct!
  values: WARN_FALSE( 1 )

assertion_macros.cpp(0): WARNING: WARN_THROWS( throw_if(false, 0) ) did NOT throw at all!

assertion_macros.cpp(0): WARNING: WARN_THROWS_WITH( throw_if(true, ""), "whops!" ) threw a DIFFERENT exception: 

assertion_macros.cpp(0): WARNING: WARN_THROWS_WITH( throw_if(false, ""), "whops!" ) did NOT throw at all!

assertion_macros.cpp(0): WARNING: WARN_THROWS_AS( throw_if(false, 0), bool ) did NOT throw at all!

assertion_macros.cpp(0): WARNING: WARN_THROWS_AS( throw_if(true, 0), bool ) threw a DIFFERENT exception: "0"

assertion_macros.cpp(0): WARNING: WARN_THROWS_WITH_AS( throw_if(false, ""), "whops!", int ) did NOT throw at all!

assertion_macros.cpp(0): WARNING: WARN_THROWS_WITH_AS( throw_if(true, ""), "whops!", int ) threw a DIFFERENT exception! (contents: )

assertion_macros.cpp(0): WARNING: WARN_NOTHROW( throw_if(true, 0) ) THREW exception: "0"

assertion_macros.cpp(0): WARNING: WARN_EQ( 1, 0 ) is NOT correct!
  values: WARN_EQ( 1, 0 )

assertion_macros.cpp(0): WARNING: WARN_UNARY( 0 ) is NOT correct!
  values: WARN_UNARY( 0 )

assertion_macros.cpp(0): WARNING: WARN_UNARY_FALSE( 1 ) is NOT correct!
  values: WARN_UNARY_FALSE( 1 )

===============================================================================
stringification.cpp(0):
TEST CASE:  a test case that registers an exception translator for int and then throws one

stringification.cpp(0): ERROR: test case THREW exception: 5

===============================================================================
logging.cpp(0):
TEST CASE:  a test case that will end from an exception

logging.cpp(0): ERROR: forcing the many captures to be stringified
  logged: lots of captures: 42 42 42;
          old way of capturing - using the streaming operator: 42 42

logging.cpp(0): ERROR: CHECK( some_var == 666 ) is NOT correct!
  values: CHECK( 42 == 666 )
  logged: someTests() returned: 42
          this should be printed if an exception is thrown even if no assert has failed: 42
          in a nested scope this should be printed as well: 42
          why is this not 666 ?!

logging.cpp(0): ERROR: test case THREW exception: 0
  logged: someTests() returned: 42
          this should be printed if an exception is thrown even if no assert has failed: 42
          in a nested scope this should be printed as well: 42

===============================================================================
logging.cpp(0):
TEST CASE:  a test case that will end from an exception and should print the unprinted context

logging.cpp(0): ERROR: test case THREW exception: 0
  logged: should be printed even if an exception is thrown and no assert fails before that

===============================================================================
stringification.cpp(0):
TEST CASE:  all asserts should fail and show how the objects get stringified

stringification.cpp(0): MESSAGE: Foo{}

stringification.cpp(0): ERROR: CHECK( f1 == f2 ) is NOT correct!
  values: CHECK( Foo{} == Foo{} )

stringification.cpp(0): MESSAGE: omg

stringification.cpp(0): ERROR: CHECK( dummy == "tralala" ) is NOT correct!
  values: CHECK( omg == tralala )

stringification.cpp(0): ERROR: CHECK( "tralala" == dummy ) is NOT correct!
  values: CHECK( tralala == omg )

stringification.cpp(0): MESSAGE: [1, 2, 3]

stringification.cpp(0): ERROR: CHECK( vec1 == vec2 ) is NOT correct!
  values: CHECK( [1, 2, 3] == [1, 2, 4] )

stringification.cpp(0): MESSAGE: [1, 42, 3]

stringification.cpp(0): ERROR: CHECK( lst_1 == lst_2 ) is NOT correct!
  values: CHECK( [1, 42, 3] == [1, 2, 666] )

stringification.cpp(0): ERROR: CHECK( s1 == s2 ) is NOT correct!
  values: CHECK( MyOtherType: 42 == MyOtherType: 666 )
  logged: s1=MyOtherType: 42 s2=MyOtherType: 666

stringification.cpp(0): ERROR: CHECK( s1 == s2 ) is NOT correct!
  values: CHECK( MyOtherType: 42 == MyOtherType: 666 )
  logged: s1=MyOtherType: 42 s2=MyOtherType: 666
          MyOtherType: 42 is not really MyOtherType: 666

stringification.cpp(0): ERROR: CHECK( "a" == doctest::Contains("aaa") ) is NOT correct!
  values: CHECK( a == Contains( aaa ) )

stringification.cpp(0): ERROR: test case THREW exception: MyTypeInherited<int>(5, 4)

===============================================================================
templated_test_cases.cpp(0):
TEST CASE:  bad stringification of type pair<int_pair>

templated_test_cases.cpp(0): ERROR: CHECK( t2 != T2() ) is NOT correct!
  values: CHECK( 0 != 0 )

===============================================================================
assertion_macros.cpp(0):
TEST CASE:  check return values

assertion_macros.cpp(0): MESSAGE: :D

assertion_macros.cpp(0): MESSAGE: :D

assertion_macros.cpp(0): MESSAGE: :D

assertion_macros.cpp(0): MESSAGE: :D

assertion_macros.cpp(0): MESSAGE: :D

assertion_macros.cpp(0): MESSAGE: :D

assertion_macros.cpp(0): MESSAGE: :D

assertion_macros.cpp(0): MESSAGE: :D

assertion_macros.cpp(0): MESSAGE: :D

===============================================================================
assertion_macros.cpp(0):
TEST CASE:  check return values no print

assertion_macros.cpp(0): ERROR: CHECK( a == b ) is NOT correct!
  values: CHECK( 4 == 2 )

assertion_macros.cpp(0): ERROR: CHECK_FALSE( a != b ) is NOT correct!
  values: CHECK_FALSE( 4 != 2 )

assertion_macros.cpp(0): ERROR: CHECK_EQ( a, b ) is NOT correct!
  values: CHECK_EQ( 4, 2 )

assertion_macros.cpp(0): ERROR: CHECK_UNARY( a == b ) is NOT correct!
  values: CHECK_UNARY( false )

assertion_macros.cpp(0): ERROR: CHECK_UNARY_FALSE( a != b ) is NOT correct!
  values: CHECK_UNARY_FALSE( true )

assertion_macros.cpp(0): ERROR: CHECK_THROWS( throw_if(false, false) ) did NOT throw at all!

assertion_macros.cpp(0): ERROR: CHECK_THROWS_AS( throw_if(true, 2), doctest::Approx ) threw a DIFFERENT exception: "2"

assertion_macros.cpp(0): ERROR: CHECK_NOTHROW( throw_if(true, 2) ) THREW exception: "2"

assertion_macros.cpp(0): ERROR: CHECK_THROWS_WITH( throw_if(true, 2), "1" ) threw a DIFFERENT exception: "2"

===============================================================================
test_cases_and_suites.cpp(0):
DESCRIPTION: regarding failures
TEST SUITE: test suite with a description
TEST CASE:  doesn't fail but it should have

Should have failed but didn't! Marking it as failed!
===============================================================================
enums.cpp(0):
TEST CASE:  enum 2

enums.cpp(0): ERROR: CHECK_EQ( castToUnderlying(Zero), 1 ) is NOT correct!
  values: CHECK_EQ( 0, 1 )

enums.cpp(0): ERROR: CHECK_EQ( castToUnderlying(One), 2 ) is NOT correct!
  values: CHECK_EQ( 1, 2 )

enums.cpp(0): ERROR: CHECK_EQ( castToUnderlying(Two), 3 ) is NOT correct!
  values: CHECK_EQ( 2, 3 )

enums.cpp(0): ERROR: CHECK_EQ( castToUnderlying(TypedZero), 1 ) is NOT correct!
  values: CHECK_EQ( 0, 1 )

enums.cpp(0): ERROR: CHECK_EQ( castToUnderlying(TypedOne), 2 ) is NOT correct!
  values: CHECK_EQ( 1, 2 )

enums.cpp(0): ERROR: CHECK_EQ( castToUnderlying(TypedTwo), 3 ) is NOT correct!
  values: CHECK_EQ( 2, 3 )

enums.cpp(0): ERROR: CHECK_EQ( EnumClassC::Zero, EnumClassC::One ) is NOT correct!
  values: CHECK_EQ( 48, 49 )

enums.cpp(0): ERROR: CHECK_EQ( EnumClassC::One, EnumClassC::Two ) is NOT correct!
  values: CHECK_EQ( 49, 50 )

enums.cpp(0): ERROR: CHECK_EQ( EnumClassC::Two, EnumClassC::Zero ) is NOT correct!
  values: CHECK_EQ( 50, 48 )

enums.cpp(0): ERROR: CHECK_EQ( EnumClassSC::Zero, EnumClassSC::One ) is NOT correct!
  values: CHECK_EQ( 48, 49 )

enums.cpp(0): ERROR: CHECK_EQ( EnumClassSC::One, EnumClassSC::Two ) is NOT correct!
  values: CHECK_EQ( 49, 50 )

enums.cpp(0): ERROR: CHECK_EQ( EnumClassSC::Two, EnumClassSC::Zero ) is NOT correct!
  values: CHECK_EQ( 50, 48 )

enums.cpp(0): ERROR: CHECK_EQ( EnumClassUC::Zero, EnumClassUC::One ) is NOT correct!
  values: CHECK_EQ( 48, 49 )

enums.cpp(0): ERROR: CHECK_EQ( EnumClassUC::One, EnumClassUC::Two ) is NOT correct!
  values: CHECK_EQ( 49, 50 )

enums.cpp(0): ERROR: CHECK_EQ( EnumClassUC::Two, EnumClassUC::Zero ) is NOT correct!
  values: CHECK_EQ( 50, 48 )

enums.cpp(0): ERROR: CHECK_EQ( EnumClassU8::Zero, EnumClassU8::One ) is NOT correct!
  values: CHECK_EQ( 0, 1 )

enums.cpp(0): ERROR: CHECK_EQ( EnumClassU8::One, EnumClassU8::Two ) is NOT correct!
  values: CHECK_EQ( 1, 2 )

enums.cpp(0): ERROR: CHECK_EQ( EnumClassU8::Two, EnumClassU8::Zero ) is NOT correct!
  values: CHECK_EQ( 2, 0 )

Failed as expected so marking it as not failed
===============================================================================
assertion_macros.cpp(0):
TEST CASE:  exceptions-related macros

assertion_macros.cpp(0): ERROR: CHECK_THROWS( throw_if(false, 0) ) did NOT throw at all!

assertion_macros.cpp(0): ERROR: CHECK_THROWS_AS( throw_if(true, 0), char ) threw a DIFFERENT exception: "0"

assertion_macros.cpp(0): ERROR: CHECK_THROWS_AS( throw_if(false, 0), int ) did NOT throw at all!

assertion_macros.cpp(0): ERROR: CHECK_THROWS_WITH( throw_if(true, "whops!"), "whops! no match!" ) threw a DIFFERENT exception: "whops!"

assertion_macros.cpp(0): ERROR: CHECK_THROWS_WITH( throw_if(true, "whops! does it match?"), "whops! no match!" ) threw a DIFFERENT exception: "whops! does it match?"

assertion_macros.cpp(0): ERROR: CHECK_THROWS_WITH_AS( throw_if(true, "whops!"), "whops! no match!", bool ) threw a DIFFERENT exception! (contents: "whops!")

assertion_macros.cpp(0): ERROR: CHECK_THROWS_WITH_AS( throw_if(true, "whops!"), "whops!", int ) threw a DIFFERENT exception! (contents: "whops!")

assertion_macros.cpp(0): ERROR: CHECK_THROWS_WITH_AS( throw_if(true, "whops! does it match?"), "whops! no match!", int ) threw a DIFFERENT exception! (contents: "whops! does it match?")

assertion_macros.cpp(0): ERROR: CHECK_NOTHROW( throw_if(true, 0) ) THREW exception: "0"

===============================================================================
assertion_macros.cpp(0):
TEST CASE:  exceptions-related macros for std::exception

assertion_macros.cpp(0): ERROR: CHECK_THROWS( throw_if(false, 0) ) did NOT throw at all!

assertion_macros.cpp(0): ERROR: CHECK_THROWS_AS( throw_if(false, std::runtime_error("whops!")), std::exception ) did NOT throw at all!

assertion_macros.cpp(0): ERROR: CHECK_THROWS_AS( throw_if(true, std::runtime_error("whops!")), int ) threw a DIFFERENT exception: "whops!"

assertion_macros.cpp(0): ERROR: CHECK_THROWS_WITH( throw_if(false, ""), "whops!" ) did NOT throw at all!

assertion_macros.cpp(0): FATAL ERROR: REQUIRE_NOTHROW( throw_if(true, std::runtime_error("whops!")) ) THREW exception: "whops!"

===============================================================================
logging.cpp(0):
TEST CASE:  explicit failures 1

logging.cpp(0): ERROR: this should not end the test case, but mark it as failing

logging.cpp(0): MESSAGE: reached!

===============================================================================
logging.cpp(0):
TEST CASE:  explicit failures 2

logging.cpp(0): FATAL ERROR: fail the test case and also end it

===============================================================================
test_cases_and_suites.cpp(0):
DESCRIPTION: regarding failures
TEST SUITE: test suite with a description
TEST CASE:  fails - and its allowed

test_cases_and_suites.cpp(0): FATAL ERROR: 

Allowed to fail so marking it as not failed
===============================================================================
test_cases_and_suites.cpp(0):
DESCRIPTION: regarding failures
TEST SUITE: test suite with a description
TEST CASE:  fails 1 time as it should

test_cases_and_suites.cpp(0): FATAL ERROR: 

Failed exactly 1 times as expected so marking it as not failed!
===============================================================================
test_cases_and_suites.cpp(0):
DESCRIPTION: regarding failures
TEST SUITE: test suite with a description
TEST CASE:  fails as it should

test_cases_and_suites.cpp(0): FATAL ERROR: 

Failed as expected so marking it as not failed
===============================================================================
subcases.cpp(0):
TEST CASE:  fails from an exception but gets re-entered to traverse all subcases
  level zero
  one

subcases.cpp(0): ERROR: CHECK( false ) is NOT correct!
  values: CHECK( false )

===============================================================================
subcases.cpp(0):
TEST CASE:  fails from an exception but gets re-entered to traverse all subcases
  level zero

DEEPEST SUBCASE STACK REACHED (DIFFERENT FROM THE CURRENT ONE):
  level zero
  one

subcases.cpp(0): ERROR: test case THREW exception: exception thrown in subcase - will translate later when the whole test case has been exited (cannot translate while there is an active exception)

===============================================================================
subcases.cpp(0):
TEST CASE:  fails from an exception but gets re-entered to traverse all subcases

DEEPEST SUBCASE STACK REACHED (DIFFERENT FROM THE CURRENT ONE):
  level zero
  one

subcases.cpp(0): ERROR: test case THREW exception: failure... but the show must go on!

===============================================================================
subcases.cpp(0):
TEST CASE:  fails from an exception but gets re-entered to traverse all subcases
  level zero
  two

subcases.cpp(0): ERROR: CHECK( false ) is NOT correct!
  values: CHECK( false )

===============================================================================
subcases.cpp(0):
TEST CASE:  fails from an exception but gets re-entered to traverse all subcases
  level zero

DEEPEST SUBCASE STACK REACHED (DIFFERENT FROM THE CURRENT ONE):
  level zero
  two

subcases.cpp(0): ERROR: test case THREW exception: exception thrown in subcase - will translate later when the whole test case has been exited (cannot translate while there is an active exception)

===============================================================================
subcases.cpp(0):
TEST CASE:  fails from an exception but gets re-entered to traverse all subcases

DEEPEST SUBCASE STACK REACHED (DIFFERENT FROM THE CURRENT ONE):
  level zero
  two

subcases.cpp(0): ERROR: test case THREW exception: failure... but the show must go on!

===============================================================================
test_cases_and_suites.cpp(0):
DESCRIPTION: regarding failures
TEST SUITE: test suite with a description
TEST CASE:  fails more times than it should

test_cases_and_suites.cpp(0): ERROR: 

test_cases_and_suites.cpp(0): ERROR: 

Didn't fail exactly 1 times so marking it as failed!
===============================================================================
test_cases_and_suites.cpp(0):
TEST CASE:  fixtured test - not part of a test suite

test_cases_and_suites.cpp(0): ERROR: CHECK( data == 85 ) is NOT correct!
  values: CHECK( 21 == 85 )

===============================================================================
header.h(0):
TEST SUITE: some TS
TEST CASE:  in TS

header.h(0): FATAL ERROR: 

===============================================================================
logging.cpp(0):
TEST CASE:  logging the counter of a loop

logging.cpp(0): ERROR: CHECK( vec[i] != (1 << i) ) is NOT correct!
  values: CHECK( 1 != 1 )
  logged: current iteration of loop:
          i := 0

logging.cpp(0): ERROR: CHECK( vec[i] != (1 << i) ) is NOT correct!
  values: CHECK( 2 != 2 )
  logged: current iteration of loop:
          i := 1

logging.cpp(0): ERROR: CHECK( vec[i] != (1 << i) ) is NOT correct!
  values: CHECK( 4 != 4 )
  logged: current iteration of loop:
          i := 2

logging.cpp(0): ERROR: CHECK( vec[i] != (1 << i) ) is NOT correct!
  values: CHECK( 8 != 8 )
  logged: current iteration of loop:
          i := 3

logging.cpp(0): ERROR: CHECK( vec[i] != (1 << i) ) is NOT correct!
  values: CHECK( 16 != 16 )
  logged: current iteration of loop:
          i := 4


root
1
1.1

root
2
2.1

root
2
===============================================================================
subcases.cpp(0):
TEST CASE:  lots of nested subcases

subcases.cpp(0): FATAL ERROR: 

===============================================================================
templated_test_cases.cpp(0):
TEST CASE:  multiple types<Custom name test>

templated_test_cases.cpp(0): ERROR: CHECK( t2 != T2() ) is NOT correct!
  values: CHECK( 0 != 0 )

===============================================================================
templated_test_cases.cpp(0):
TEST CASE:  multiple types<Other custom name>

templated_test_cases.cpp(0): ERROR: CHECK( t2 != T2() ) is NOT correct!
  values: CHECK( 0 != 0 )

===============================================================================
templated_test_cases.cpp(0):
TEST CASE:  multiple types<TypePair<bool, int>>

templated_test_cases.cpp(0): ERROR: CHECK( t2 != T2() ) is NOT correct!
  values: CHECK( 0 != 0 )

===============================================================================
stringification.cpp(0):
TEST CASE:  no headers

stringification.cpp(0): MESSAGE: 1as

stringification.cpp(0): ERROR: CHECK( chs == nullptr ) is NOT correct!
  values: CHECK( 1as == nullptr )

stringification.cpp(0): MESSAGE: 1as

stringification.cpp(0): ERROR: CHECK( "1as" == nullptr ) is NOT correct!
  values: CHECK( 1as == nullptr )

stringification.cpp(0): MESSAGE: [0, 1, 1, 2, 3, 5, 8, 13]

stringification.cpp(0): ERROR: CHECK( ints == nullptr ) is NOT correct!
  values: CHECK( [0, 1, 1, 2, 3, 5, 8, 13] == nullptr )

stringification.cpp(0): MESSAGE: [0, 1, 1, 2, 3, 5, 8, 13]

stringification.cpp(0): MESSAGE: nullptr

stringification.cpp(0): ERROR: CHECK( cnptr != nullptr ) is NOT correct!
  values: CHECK( nullptr != nullptr )

stringification.cpp(0): MESSAGE: 0

stringification.cpp(0): ERROR: CHECK( A == C ) is NOT correct!
  values: CHECK( 0 == 100 )

stringification.cpp(0): MESSAGE: int

===============================================================================
assertion_macros.cpp(0):
TEST CASE:  normal macros

assertion_macros.cpp(0): ERROR: CHECK( throw_if(true, std::runtime_error("whops!")) == 42 ) THREW exception: "whops!"

assertion_macros.cpp(0): ERROR: CHECK( doctest::Approx(0.502) == 0.501 ) is NOT correct!
  values: CHECK( Approx( 0.502 ) == 0.501 )

===============================================================================
test_cases_and_suites.cpp(0):
TEST SUITE: ts1
TEST CASE:  normal test in a test suite from a decorator

test_cases_and_suites.cpp(0): MESSAGE: failing because of the timeout decorator!

Test case exceeded time limit of 0.000001!
===============================================================================
stringification.cpp(0):
TEST CASE:  operator<<

stringification.cpp(0): MESSAGE: A

stringification.cpp(0): MESSAGE: B

stringification.cpp(0): MESSAGE: C

===============================================================================
test_cases_and_suites.cpp(0):
TEST SUITE: scoped test suite
TEST CASE:  part of scoped

test_cases_and_suites.cpp(0): FATAL ERROR: 

===============================================================================
test_cases_and_suites.cpp(0):
TEST SUITE: scoped test suite
TEST CASE:  part of scoped 2

test_cases_and_suites.cpp(0): FATAL ERROR: 

===============================================================================
test_cases_and_suites.cpp(0):
TEST SUITE: some TS
TEST CASE:  part of some TS

test_cases_and_suites.cpp(0): FATAL ERROR: 


root
outside of subcase
inside subcase 0
outside of subcase
inside subcase 1
outside of subcase
inside subcase 2

root
outside of subcase
also inside 0
outside of subcase
also inside 1
outside of subcase
also inside 2

root
outside of subcase
fail inside 0
outside of subcase
===============================================================================
subcases.cpp(0):
TEST CASE:  reentering subcase via regular control flow

DEEPEST SUBCASE STACK REACHED (DIFFERENT FROM THE CURRENT ONE):

subcases.cpp(0): FATAL ERROR: 1


root
outside of subcase
inside outside
nested twice 0, 0
nested twice 0, 1
nested twice 0, 2
outside of subcase
inside outside
nested twice 1, 0
nested twice 1, 1
nested twice 1, 2
outside of subcase
inside outside
nested twice 2, 0
nested twice 2, 1
nested twice 2, 2

root
outside of subcase
inside outside
also twice 0, 0
also twice 0, 1
also twice 0, 2
outside of subcase
inside outside
also twice 1, 0
also twice 1, 1
also twice 1, 2
outside of subcase
inside outside
also twice 2, 0
also twice 2, 1
also twice 2, 2
===============================================================================
test_cases_and_suites.cpp(0):
TEST CASE:  should fail because of an exception

test_cases_and_suites.cpp(0): ERROR: test case THREW exception: 0

===============================================================================
assertion_macros.cpp(0):
TEST CASE:  some asserts used in a function called by a test case

assertion_macros.cpp(0): ERROR: CHECK_THROWS_WITH_AS( throw_if(true, false), "unknown exception", int ) threw a DIFFERENT exception! (contents: "unknown exception")

===============================================================================
subcases.cpp(0):
TEST CASE:  subcases can be used in a separate function as well
  from function...

subcases.cpp(0): MESSAGE: print me twice

===============================================================================
subcases.cpp(0):
TEST CASE:  subcases can be used in a separate function as well
  from function...
  sc1

subcases.cpp(0): MESSAGE: hello! from sc1

===============================================================================
subcases.cpp(0):
TEST CASE:  subcases can be used in a separate function as well

DEEPEST SUBCASE STACK REACHED (DIFFERENT FROM THE CURRENT ONE):
  from function...
  sc1

subcases.cpp(0): MESSAGE: lala

===============================================================================
subcases.cpp(0):
TEST CASE:  subcases can be used in a separate function as well
  from function...

subcases.cpp(0): MESSAGE: print me twice

===============================================================================
subcases.cpp(0):
TEST CASE:  subcases can be used in a separate function as well
  from function...
  sc2

subcases.cpp(0): MESSAGE: hello! from sc2

===============================================================================
subcases.cpp(0):
TEST CASE:  subcases can be used in a separate function as well

DEEPEST SUBCASE STACK REACHED (DIFFERENT FROM THE CURRENT ONE):
  from function...
  sc2

subcases.cpp(0): MESSAGE: lala

===============================================================================
subcases.cpp(0):
TEST CASE:  subcases with changing names
  outer 0
  inner 0

subcases.cpp(0): MESSAGE: msg!

===============================================================================
subcases.cpp(0):
TEST CASE:  subcases with changing names
  outer 0
  inner 1

subcases.cpp(0): MESSAGE: msg!

===============================================================================
subcases.cpp(0):
TEST CASE:  subcases with changing names
  outer 1
  inner 0

subcases.cpp(0): MESSAGE: msg!

===============================================================================
subcases.cpp(0):
TEST CASE:  subcases with changing names
  outer 1
  inner 1

subcases.cpp(0): MESSAGE: msg!

===============================================================================
subcases.cpp(0):
TEST CASE:  subcases with changing names
  separate

subcases.cpp(0): MESSAGE: separate msg!

===============================================================================
header.h(0):
TEST CASE:  template 1<char>

header.h(0): FATAL ERROR: 

===============================================================================
header.h(0):
TEST CASE:  template 2<doctest::String>

header.h(0): FATAL ERROR: 

===============================================================================
subcases.cpp(0):
TEST CASE:  test case should fail even though the last subcase passes
  one

subcases.cpp(0): ERROR: CHECK( false ) is NOT correct!
  values: CHECK( false )

===============================================================================
logging.cpp(0):
TEST CASE:  third party asserts can report failures to doctest

logging.cpp(0): ERROR: MY_ASSERT(false)

logging.cpp(0): FATAL ERROR: MY_ASSERT_FATAL(false)

===============================================================================
test_cases_and_suites.cpp(0):
DESCRIPTION: this test has overridden its skip decorator
TEST SUITE: skipped test cases
TEST CASE:  unskipped

test_cases_and_suites.cpp(0): FATAL ERROR: 

===============================================================================
templated_test_cases.cpp(0):
TEST CASE:  vector stuff<std::vector<int>>

templated_test_cases.cpp(0): ERROR: CHECK( vec.size() == 20 ) is NOT correct!
  values: CHECK( 10 == 20 )

===============================================================================
subcases.cpp(0):
TEST SUITE: with a funny name,
TEST CASE:  with a funnier name\:
  with the funniest name\,

subcases.cpp(0): MESSAGE: Yes!

===============================================================================
subcases.cpp(0):
TEST SUITE: with a funny name,
TEST CASE:  with a funnier name\:
  with a slightly funny name :

subcases.cpp(0): MESSAGE: Yep!

===============================================================================
subcases.cpp(0):
TEST SUITE: with a funny name,
TEST CASE:  with a funnier name\:
  without a funny name

subcases.cpp(0): MESSAGE: NO!

===============================================================================
subcases.cpp(0):
TEST SUITE: with a funny name,
TEST CASE:  without a funny name:

subcases.cpp(0): MESSAGE: Nooo

===============================================================================
[doctest] test cases:  86 |  35 passed |  51 failed |
[doctest] assertions: 235 | 115 passed | 120 failed |
[doctest] Status: FAILURE!
Program code.
