#
#  Copyright © 2025 Present, Arkin Terl<PERSON>. All rights reserved.
#
#  NOTICE:  All information contained herein is, and remains the property of Arkin Terli.
#  The intellectual and technical concepts contained herein are proprietary to Arkin Terli
#  and may be covered by U.S. and Foreign Patents, patents in process, and are protected by
#  trade secret or copyright law. Dissemination of this information or reproduction of this
#  material is strictly forbidden unless prior written permission is obtained from Arkin Terli.

set(TARGET_NAME SimpleGUI)

add_executable(${TARGET_NAME}
        main.cpp
)

add_dependencies(${TARGET_NAME} wxwidgets_cpp)

    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -D__WXOSX_COCOA__ -D__WXMAC__ -D__WXOSX__ -D_FILE_OFFSET_BITS=64")
    target_link_libraries(${TARGET_NAME} PRIVATE
            "-framework AudioToolbox"
            "-framework WebKit"
            "-framework CoreFoundation"
            "-framework Security"
            "-framework Carbon"
            "-framework Cocoa"
            "-framework IOKit"
            "-framework QuartzCore"
            wx_baseu-3.3
            wx_baseu_net-3.3
            wx_baseu_xml-3.3
            wx_osx_cocoau_adv-3.3
            wx_osx_cocoau_aui-3.3
            wx_osx_cocoau_core-3.3
            wx_osx_cocoau_gl-3.3
            wx_osx_cocoau_html-3.3
            wx_osx_cocoau_media-3.3
            wx_osx_cocoau_propgrid-3.3
            wx_osx_cocoau_qa-3.3
            wx_osx_cocoau_ribbon-3.3
            wx_osx_cocoau_richtext-3.3
            wx_osx_cocoau_stc-3.3
            wx_osx_cocoau_webview-3.3
            wx_osx_cocoau_xrc-3.3
            wxjpeg-3.3
            wxlexilla-3.3
            wxpng-3.3
            wxregexu-3.3
            wxscintilla-3.3
            wxtiff-3.3
            z
            iconv.2
    )

install(TARGETS ${TARGET_NAME} DESTINATION ${CMAKE_INSTALL_PREFIX})
