//
//  Copyright © 2024-Present, Arkin Terli. All rights reserved.
//
//  NOTICE:  All information contained herein is, and remains the property of Arkin Terli.
//  The intellectual and technical concepts contained herein are proprietary to Arkin Terli
//  and may be covered by U.S. and Foreign Patents, patents in process, and are protected by
//  trade secret or copyright law. Dissemination of this information or reproduction of this
//  material is strictly forbidden unless prior written permission is obtained from Arkin Terli.

// Project includes
// External includes
#include "rpc/server.h"
// System includes


int main(int, char**)
{
    // Creating a server that listens on port 8080.
    rpc::server srv(8080);

    // Binding a lambda function to the name "add".
    srv.bind("add", [](const int a, const int b)
    {
        return a + b;
    });

    // Binding a lambda function to the name "process_bytes".
    srv.bind("process_bytes", [](std::vector<char> data) -> bool
    {
        return data.back();
    });

    // Run the server loop.
    srv.run();

    return 0;
}
