
The Google Mock class generator is an application that is part of cppclean.
For more information about cppclean, visit http://code.google.com/p/cppclean/

The mock generator requires Python 2.3.5 or later.  If you don't have Python
installed on your system, you will also need to install it.  You can download
Python from:  http://www.python.org/download/releases/

To use the Google Mock class generator, you need to call it
on the command line passing the header file and class for which you want
to generate a Google Mock class.

Make sure to install the scripts somewhere in your path.  Then you can
run the program.

  gmock_gen.py header-file.h [ClassName]...

If no ClassNames are specified, all classes in the file are emitted.

To change the indentation from the default of 2, set INDENT in
the environment.  For example to use an indent of 4 spaces:

INDENT=4 gmock_gen.py header-file.h ClassName

This version was made from SVN revision 281 in the cppclean repository.

Known Limitations
-----------------
Not all code will be generated properly.  For example, when mocking templated
classes, the template information is lost.  You will need to add the template
information manually.

Not all permutations of using multiple pointers/references will be rendered
properly.  These will also have to be fixed manually.
