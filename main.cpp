#include "SpeechRecognizer.h"
#include <CoreFoundation/CoreFoundation.h>
#include <iostream>
#include <chrono>
#include <thread>

int main()
{
    try
    {
        std::cout << "Initializing speech recognizer..." << std::endl;
        SpeechRecognizer recognizer;

        std::cout << "Starting speech recognition for 10 seconds..." << std::endl;

        recognizer.startListening([](const std::string& text)
        {
            std::cout << "Recognized text: " << text << std::endl;
        });

        std::cout << "Listening... (waiting 10 seconds)" << std::endl;

        CFRunLoopRunInMode(kCFRunLoopDefaultMode, 10.0, false);

        std::cout << "Stopping speech recognition..." << std::endl;
        recognizer.stopListening();

        // Give the system a moment to complete cleanup tasks
        std::cout << "Cleaning up audio resources..." << std::endl;
//        CFRunLoopRunInMode(kCFRunLoopDefaultMode, 0.5, false);

        std::cout << "Speech recognition stopped." << std::endl;
    }
    catch (const std::exception& e)
    {
        std::cerr << "Exception: " << e.what() << std::endl;
        return 1;
    }
    catch (...)
    {
        std::cerr << "Unknown exception occurred" << std::endl;
        return 1;
    }

    return 0;
}
