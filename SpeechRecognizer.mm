#import <Speech/Speech.h>
#import <AVFoundation/AVFoundation.h>
#include "SpeechRecognizer.h"
#include <iostream>
#include <utility>

@interface SpeechRecognizerDelegate : NSObject <SFSpeechRecognizerDelegate>
@end

@implementation SpeechRecognizerDelegate
- (void)speechRecognizer:(SFSpeechRecognizer *)speechRecognizer availabilityDidChange:(BOOL)available
{
    if (available)
    {
        NSLog(@"Speech recognition is available.");
    }
    else
    {
        NSLog(@"Speech recognition is not available.");
    }
}
@end

class SpeechRecognizer::Impl
{
public:
    SFSpeechRecognizer*        m_recognizer;
    AVAudioEngine*             m_audioEngine;
    SFSpeechRecognitionTask*   m_recognitionTask{};
    AVAudioInputNode*          m_inputNode;
    SpeechRecognizerDelegate*  m_delegate;
    SFSpeechAudioBufferRecognitionRequest* m_recognitionRequest{};
    SpeechRecognizer::RecognitionCallback  m_callback;
    bool                       m_tapInstalled{false};

    Impl()
    {
        m_delegate = [[SpeechRecognizerDelegate alloc] init];
        m_recognizer = [[SFSpeechRecognizer alloc] initWithLocale:[NSLocale localeWithLocaleIdentifier:@"en-US"]];
        m_recognizer.delegate = m_delegate;

        [SFSpeechRecognizer requestAuthorization:^(SFSpeechRecognizerAuthorizationStatus status)
        {
            dispatch_async(dispatch_get_main_queue(), ^{
                switch (status)
                {
                    case SFSpeechRecognizerAuthorizationStatusAuthorized:
                        NSLog(@"Speech recognition authorized.");
                        break;
                    default:
                        NSLog(@"Speech recognition not authorized.");
                        break;
                }
            });
        }];

        m_audioEngine = [[AVAudioEngine alloc] init];
        m_inputNode = m_audioEngine.inputNode;
    }

    ~Impl()
    {
        stopListening();
    }

    void startListening(SpeechRecognizer::RecognitionCallback cb)
    {
        m_callback = std::move(cb);

        if (m_recognitionTask)
        {
            [m_recognitionTask cancel];
            m_recognitionTask = nil;
        }

        m_recognitionRequest = [[SFSpeechAudioBufferRecognitionRequest alloc] init];
        m_recognitionRequest.shouldReportPartialResults = YES;

        m_recognitionTask = [m_recognizer recognitionTaskWithRequest:m_recognitionRequest resultHandler:^(SFSpeechRecognitionResult * _Nullable result, NSError * _Nullable error)
        {
            if (result)
            {
                NSString *bestString = result.bestTranscription.formattedString;
                if (m_callback)
                {
                    m_callback([bestString UTF8String]);
                }
            }

            if (error)
            {
                [m_audioEngine stop];
                if (m_inputNode && m_tapInstalled)
                {
                    [m_inputNode removeTapOnBus:0];
                    m_tapInstalled = false;
                }
                m_recognitionRequest = nil;
                m_recognitionTask = nil;
            }
        }];

        AVAudioFormat *recordingFormat = [m_inputNode outputFormatForBus:0];
        [m_inputNode installTapOnBus:0 bufferSize:1024 format:recordingFormat block:^(AVAudioPCMBuffer * _Nonnull buffer, AVAudioTime * _Nonnull /*when*/)
        {
            [m_recognitionRequest appendAudioPCMBuffer:buffer];
        }];
        m_tapInstalled = true;

        [m_audioEngine prepare];
        NSError* error = nil;
        [m_audioEngine startAndReturnError:&error];
        if (error)
        {
            NSLog(@"Error starting audio engine: %@", error);
        }
    }

    void stopListening()
    {
        // Cancel the recognition task first
        if (m_recognitionTask)
        {
            [m_recognitionTask cancel];
            m_recognitionTask = nil;
        }

        // End the recognition request
        if (m_recognitionRequest)
        {
            [m_recognitionRequest endAudio];
            m_recognitionRequest = nil;
        }

        // Remove the tap from the input node before stopping the engine
        if (m_inputNode && m_tapInstalled)
        {
            [m_inputNode removeTapOnBus:0];
            m_tapInstalled = false;
        }

        // Stop the audio engine
        if ([m_audioEngine isRunning])
        {
            [m_audioEngine stop];
        }

        // Reset references to ensure clean state
        m_audioEngine = nil;
        m_inputNode = nil;
    }
};

SpeechRecognizer::SpeechRecognizer() : pimpl(new Impl()) {}

SpeechRecognizer::~SpeechRecognizer()
{
    // Ensure cleanup happens synchronously
    if (pimpl)
    {
        pimpl->stopListening();
        delete pimpl;
        pimpl = nullptr;
    }
}

void SpeechRecognizer::startListening(RecognitionCallback callback) { pimpl->startListening(std::move(callback)); }
void SpeechRecognizer::stopListening() { pimpl->stopListening(); }
