#import <Speech/Speech.h>
#import <AVFoundation/AVFoundation.h>
#include "SpeechRecognizer.h"
#include <iostream>
#include <utility>

@interface SpeechRecognizerDelegate : NSObject <SFSpeechRecognizerDelegate>
@end

@implementation SpeechRecognizerDelegate
- (void)speechRecognizer:(SFSpeechRecognizer *)speechRecognizer availabilityDidChange:(BOOL)available
{
    if (available)
    {
        NSLog(@"Speech recognition is available.");
    }
    else
    {
        NSLog(@"Speech recognition is not available.");
    }
}
@end

class SpeechRecognizer::Impl
{
public:
    SFSpeechRecognizer*        recognizer;
    AVAudioEngine*             audioEngine;
    SFSpeechRecognitionTask*   recognitionTask{};
    AVAudioInputNode*          inputNode;
    SpeechRecognizerDelegate*  delegate;
    SFSpeechAudioBufferRecognitionRequest* recognitionRequest{};
    SpeechRecognizer::RecognitionCallback  callback;

    Impl()
    {
        delegate = [[SpeechRecognizerDelegate alloc] init];
        recognizer = [[SFSpeechRecognizer alloc] initWithLocale:[NSLocale localeWithLocaleIdentifier:@"en-US"]];
        recognizer.delegate = delegate;

        [SFSpeechRecognizer requestAuthorization:^(SFSpeechRecognizerAuthorizationStatus status)
        {
            dispatch_async(dispatch_get_main_queue(), ^{
                switch (status)
                {
                    case SFSpeechRecognizerAuthorizationStatusAuthorized:
                        NSLog(@"Speech recognition authorized.");
                        break;
                    default:
                        NSLog(@"Speech recognition not authorized.");
                        break;
                }
            });
        }];

        audioEngine = [[AVAudioEngine alloc] init];
        inputNode = audioEngine.inputNode;
    }

    ~Impl()
    {
        stopListening();
    }

    void startListening(SpeechRecognizer::RecognitionCallback cb)
    {
        callback = std::move(cb);

        if (recognitionTask)
        {
            [recognitionTask cancel];
            recognitionTask = nil;
        }

        recognitionRequest = [[SFSpeechAudioBufferRecognitionRequest alloc] init];
        recognitionRequest.shouldReportPartialResults = YES;

        recognitionTask = [recognizer recognitionTaskWithRequest:recognitionRequest resultHandler:^(SFSpeechRecognitionResult * _Nullable result, NSError * _Nullable error)
        {
            if (result)
            {
                NSString *bestString = result.bestTranscription.formattedString;
                if (callback)
                {
                    callback([bestString UTF8String]);
                }
            }

            if (error)
            {
                [audioEngine stop];
                [inputNode removeTapOnBus:0];
                recognitionRequest = nil;
                recognitionTask = nil;
            }
        }];

        AVAudioFormat *recordingFormat = [inputNode outputFormatForBus:0];
        [inputNode installTapOnBus:0 bufferSize:1024 format:recordingFormat block:^(AVAudioPCMBuffer * _Nonnull buffer, AVAudioTime * _Nonnull /*when*/)
        {
            [recognitionRequest appendAudioPCMBuffer:buffer];
        }];

        [audioEngine prepare];
        NSError* error = nil;
        [audioEngine startAndReturnError:&error];
        if (error)
        {
            NSLog(@"Error starting audio engine: %@", error);
        }
    }

    void stopListening()
    {
        if ([audioEngine isRunning])
        {
            [audioEngine stop];
            [recognitionRequest endAudio];
            recognitionRequest = nil;
            recognitionTask = nil;
            // Release the audio engine and input node.
            audioEngine = nil;
            inputNode = nil;
        }
    }

};

SpeechRecognizer::SpeechRecognizer() : pimpl(new Impl()) {}
SpeechRecognizer::~SpeechRecognizer()
{
    stopListening();
    // Add a delay to ensure that the audio engine is stopped
    dispatch_async(dispatch_get_main_queue(), ^{
        delete pimpl;
    });
}
void SpeechRecognizer::startListening(RecognitionCallback callback) { pimpl->startListening(std::move(callback)); }
void SpeechRecognizer::stopListening() { pimpl->stopListening(); }
